[2025-06-03 16:30:51] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AhqwyGjjgnWxrfjo1cqnGDi"}}  
[2025-06-03 16:31:56] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AQV9RCNibaY5zm53DcndLKM"}}  
[2025-06-03 16:33:01] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A6fwHPy_8A_1Yw03hn5qObz"}}  
[2025-06-03 16:34:06] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"ACqvuAiJEKdTZ7WQN4nCTKz"}}  
[2025-06-03 16:35:11] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AUaLVVUrc08Vk7ku5ox32Ku"}}  
[2025-06-03 16:36:16] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"ACZpWc5c9M2MK92LDhIZQUo"}}  
[2025-06-03 16:37:01] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"AOHA0k0rG0nloSDyxY3vUSV"}}  
[2025-06-03 16:37:06] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"ARxDDA_0IifmbjAYx73gfYI"}}  
[2025-06-03 16:38:30] local.ERROR: Failed to get block users:  {"error":{"message":"Invalid OAuth access token - Cannot parse access token","type":"OAuthException","code":190,"fbtrace_id":"A8XnRv3acHVwvOfx5XzQrMm"}}  
[2025-06-03 16:55:27] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "Pending" does not exist
LINE 2:         WHEN task_status = 0 THEN "Pending" 
                                          ^ (Connection: pgsql, SQL: select CASE 
        WHEN task_status = 0 THEN "Pending" 
        WHEN task_status = 1 THEN "Sent" 
        WHEN task_status = 2 THEN "Delivered"
        WHEN task_status = 3 THEN "Read"
        WHEN task_status = 4 THEN "Failed"
        ELSE "Unknown" 
        END as type, count(*) as task from "task" where "created_by" = 2 and "scheduled_on" between 2025-05-27 00:00:00 and 2025-06-03 23:59:59 group by "task_status") {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"Pending\" does not exist
LINE 2:         WHEN task_status = 0 THEN \"Pending\" 
                                          ^ (Connection: pgsql, SQL: select CASE 

        WHEN task_status = 0 THEN \"Pending\" 

        WHEN task_status = 1 THEN \"Sent\" 

        WHEN task_status = 2 THEN \"Delivered\"

        WHEN task_status = 3 THEN \"Read\"

        WHEN task_status = 4 THEN \"Failed\"

        ELSE \"Unknown\" 

        END as type, count(*) as task from \"task\" where \"created_by\" = 2 and \"scheduled_on\" between 2025-05-27 00:00:00 and 2025-06-03 23:59:59 group by \"task_status\") at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select CASE \\r\\n ...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select CASE \\r\\n ...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select CASE \\r\\n ...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(131): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(84): App\\Http\\Controllers\\User\\DashboardController->messagesStatics(7)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\DashboardController->dashboardData()
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('dashboardData', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\DashboardController), 'dashboardData')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(165): App\\Http\\Controllers\\User\\DashboardController->App\\Http\\Controllers\\User\\{closure}(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}

[previous exception] [object] (PDOException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"Pending\" does not exist
LINE 2:         WHEN task_status = 0 THEN \"Pending\" 
                                          ^ at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:428)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(428): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select CASE \\r\\n ...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select CASE \\r\\n ...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select CASE \\r\\n ...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select CASE \\r\\n ...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(131): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(84): App\\Http\\Controllers\\User\\DashboardController->messagesStatics(7)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\DashboardController->dashboardData()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('dashboardData', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\DashboardController), 'dashboardData')
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(165): App\\Http\\Controllers\\User\\DashboardController->App\\Http\\Controllers\\User\\{closure}(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 {main}
"} 
[2025-06-03 16:58:12] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "Pending" does not exist
LINE 2:         WHEN task_status = 0 THEN "Pending" 
                                          ^ (Connection: pgsql, SQL: select CASE 
        WHEN task_status = 0 THEN "Pending" 
        WHEN task_status = 1 THEN "Sent" 
        WHEN task_status = 2 THEN "Delivered"
        WHEN task_status = 3 THEN "Read"
        WHEN task_status = 4 THEN "Failed"
        ELSE "Unknown" 
        END as type, count(*) as task from "task" where "created_by" = 2 and "scheduled_on" between 2025-05-27 00:00:00 and 2025-06-03 23:59:59 group by "task_status") {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"Pending\" does not exist
LINE 2:         WHEN task_status = 0 THEN \"Pending\" 
                                          ^ (Connection: pgsql, SQL: select CASE 

        WHEN task_status = 0 THEN \"Pending\" 

        WHEN task_status = 1 THEN \"Sent\" 

        WHEN task_status = 2 THEN \"Delivered\"

        WHEN task_status = 3 THEN \"Read\"

        WHEN task_status = 4 THEN \"Failed\"

        ELSE \"Unknown\" 

        END as type, count(*) as task from \"task\" where \"created_by\" = 2 and \"scheduled_on\" between 2025-05-27 00:00:00 and 2025-06-03 23:59:59 group by \"task_status\") at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select CASE \\r\\n ...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select CASE \\r\\n ...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select CASE \\r\\n ...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(131): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(84): App\\Http\\Controllers\\User\\DashboardController->messagesStatics(7)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\DashboardController->dashboardData()
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('dashboardData', Array)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\DashboardController), 'dashboardData')
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(165): App\\Http\\Controllers\\User\\DashboardController->App\\Http\\Controllers\\User\\{closure}(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}

[previous exception] [object] (PDOException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"Pending\" does not exist
LINE 2:         WHEN task_status = 0 THEN \"Pending\" 
                                          ^ at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:428)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(428): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select CASE \\r\\n ...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select CASE \\r\\n ...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select CASE \\r\\n ...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select CASE \\r\\n ...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(131): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(84): App\\Http\\Controllers\\User\\DashboardController->messagesStatics(7)
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\DashboardController->dashboardData()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('dashboardData', Array)
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\DashboardController), 'dashboardData')
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\DashboardController.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(165): App\\Http\\Controllers\\User\\DashboardController->App\\Http\\Controllers\\User\\{closure}(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 {main}
"} 
[2025-06-03 16:58:32] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "device_id" does not exist
LINE 1: ...elect * from "templates" where "user_id" = $1 and "device_id...
                                                             ^ (Connection: pgsql, SQL: select * from "templates" where "user_id" = 2 and "device_id" in (19, 1, 7, 38) and "status" = 1) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"device_id\" does not exist
LINE 1: ...elect * from \"templates\" where \"user_id\" = $1 and \"device_id...
                                                             ^ (Connection: pgsql, SQL: select * from \"templates\" where \"user_id\" = 2 and \"device_id\" in (19, 1, 7, 38) and \"status\" = 1) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\TemplateController.php(32): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\TemplateController->index(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\TemplateController), 'index')
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 {main}

[previous exception] [object] (PDOException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"device_id\" does not exist
LINE 1: ...elect * from \"templates\" where \"user_id\" = $1 and \"device_id...
                                                             ^ at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:428)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(428): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\TemplateController.php(32): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\TemplateController->index(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\TemplateController), 'index')
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 {main}
"} 
[2025-06-03 16:58:40] local.ERROR: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "device_id" does not exist
LINE 1: ...elect * from "templates" where "user_id" = $1 and "device_id...
                                                             ^ (Connection: pgsql, SQL: select * from "templates" where "user_id" = 2 and "device_id" in (19, 1, 7, 38) and "status" = 1) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"device_id\" does not exist
LINE 1: ...elect * from \"templates\" where \"user_id\" = $1 and \"device_id...
                                                             ^ (Connection: pgsql, SQL: select * from \"templates\" where \"user_id\" = 2 and \"device_id\" in (19, 1, 7, 38) and \"status\" = 1) at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\TemplateController.php(32): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\TemplateController->index(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\TemplateController), 'index')
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 {main}

[previous exception] [object] (PDOException(code: 42703): SQLSTATE[42703]: Undefined column: 7 ERROR:  column \"device_id\" does not exist
LINE 1: ...elect * from \"templates\" where \"user_id\" = $1 and \"device_id...
                                                             ^ at C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:428)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(428): PDOStatement->execute()
#1 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2871): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2860): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3414): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2859): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(738): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(722): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Controllers\\User\\TemplateController.php(32): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\User\\TemplateController->index(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\User\\TemplateController), 'index')
#14 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\CheckPermission.php(47): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckPermission->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\SaasMiddleware.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SaasMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\UserMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\app\\Http\\Middleware\\EnvironmentMiddleware.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\EnvironmentMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\OneDrive\\Vishal\\xampp\\htdocs\\waba_panelv2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 {main}
"} 
