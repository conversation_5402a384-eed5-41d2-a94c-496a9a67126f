<?php

namespace App\Http\Middleware;

use App\Models\Device;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Models\User;
use Illuminate\Support\Facades\Session;

class SaasMiddleware
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // if (Auth::user() && Auth::user()->parent) {
        //     $user_id = Auth::user()->parent->id;
        // } else {
        //     $user_id = Auth::id();
        // }
        // $user = Cache::remember("user_{$user_id}_" . session()->getId(), now()->addMinutes(10), function () use ($user_id) {
        //     return User::find($user_id);
        // });

        // if ($request->is('user/subscription*') || $request->is('user/dashboard*') || $request->is('user/agent*') || $request->is('user/support*') || $request->is('user/profile*') || $request->is('user/make-subscribe/*') || $request->is('agent/dashboard*') || $request->is('agent/support*') || $request->is('agent/profile*')) {

        //     return $next($request);
        // }

        // if ($user->will_expire == null) {
        //     Session::flash('saas_error', __('Your subscription payment is not completed'));

        //     //deactivate the devices
        //     Device::where('user_id', $user_id)->update(['status' => 0]);

        //     if (Auth::user()->role == 'agent') {
        //         return redirect('/agent/dashboard');
        //     }

        //     $redirect_url = $user->plan_id == null ? '/user/subscription' : '/user/subscription/' . $user->plan_id;
        //     return redirect($redirect_url);
        // }

        // if ($user->will_expire < now()) {

        //     //deactivate the devices
        //     Device::where('user_id', $user_id)->update(['status' => 0]);

        //     Session::flash('saas_error', __('Your subscription payment was expired please renew the subscription'));

        //     if (Auth::user()->role == 'agent') {
        //         return redirect('/agent/dashboard');
        //     }
        //     return redirect('/user/dashboard');
        // }

        return $next($request);
    }
}
