<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Models\Device;
use App\Models\Group;
use App\Models\Plan;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Order;
use App\Models\Smstransaction;
use App\Traits\Notifications;
// use Auth;
use Str;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class CustomerController extends Controller
{

    use Notifications;

    public function __construct()
    {
        $this->middleware('permission:customer');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $customers = User::query();

        if (!empty($request->search)) {
            $customers->whereRaw('LOWER(' . $request->type . ') LIKE ?', ['%' . strtolower($request->search) . '%']);
        }

        $login_id = Auth::id();
        if ($login_id == 1) {
            $is_subadmin = false;
        } else {
            $is_subadmin = true;
        }
        $customers = $customers->where('role', 'user')
            ->when($is_subadmin, function ($query) use ($login_id) {
                return $query->where('reseller_id', $login_id);
            })
            ->with('subscription')->withCount('orders')->latest()->paginate(20);

        $type = $request->type ?? '';

        $totalCustomers = User::where('role', 'user')
            ->when($is_subadmin, function ($query) use ($login_id) {
                return $query->where('reseller_id', $login_id);
            })->count();

        $totalActiveCustomers = User::where('role', 'user')->when($is_subadmin, function ($query) use ($login_id) {
            return $query->where('reseller_id', $login_id);
        })->where('status', 1)->count();
        $totalSuspendedCustomers = User::where('role', 'user')->when($is_subadmin, function ($query) use ($login_id) {
            return $query->where('reseller_id', $login_id);
        })->where('status', 0)->count();
        $totalExpiredCustomers = User::where('role', 'user')->when($is_subadmin, function ($query) use ($login_id) {
            return $query->where('reseller_id', $login_id);
        })->where('will_expire', '<=', now())->count();

        return view('admin.customers.index', compact('customers', 'request', 'type', 'totalCustomers', 'totalActiveCustomers', 'totalSuspendedCustomers', 'totalExpiredCustomers'));
    }

    public function create()
    {
        return view('admin.customers.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'phone' => 'nullable|numeric|unique:users,phone',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:8|max:100',
        ]);

        User::create([
            'name' => $request->name,
            'phone' => $request->phone ?? null,
            'email' => $request->email,
            'reseller_id' => Auth::id(),
            'role' => 'user',
            'status' => 1,
            'plan_id' => null,
            'will_expire' => null,
            'authkey' => $this->generateAuthKey(),
            'password' => Hash::make($request->password)
        ]);

        return response()->json([
            'redirect' => route('admin.customer.index'),
            'message' => __('User Created successfully.')
        ]);
    }

    public function generateAuthKey()
    {
        $rend = Str::random(50);
        $check = User::where('authkey', $rend)->first();

        if ($check == true) {
            $rend = $this->generateAuthKey();
        }
        return $rend;
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $customer = User::query()->withCount('orders')->withCount('contact')->withCount('device')->withSum('orders', 'amount')->withCount('smstransaction')->with('subscription')->findorFail($id);
        $orders = Order::where('user_id', $id)->with('plan', 'gateway')->latest()->paginate(20);

        return view('admin.customers.show', compact('customer', 'orders'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $customer = User::query()->where('role', 'user')->findorFail($id);

        return view('admin.customers.edit', compact('customer'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'password' => ['nullable', 'min:8', 'max:100'],
            'name' => ['required', 'string'],
            'email' => 'required|email|unique:users,email,' . $id,
            'phone' => 'nullable|numeric|unique:users,phone,' . $id,
        ]);

        $customer = User::query()->where('role', 'user')->findorFail($id);
        $customer->name = $request->name;
        $customer->email = $request->email;
        $customer->status = $request->status;
        $customer->phone = $request->phone;
        $customer->address = $request->address;
        if ($request->password) {
            $customer->password = Hash::make($request->password);
        }
        $customer->save();

        $title = 'Your account information has changed by admin';

        $notification['user_id'] = $customer->id;
        $notification['title'] = $title;
        $notification['url'] = '/user/profile';

        try {
            $this->createNotification($notification);
        } catch (\Exception $e) {
            return response()->json([
                'message' => __('Notification Not Save.')
            ]);
        }

        return response()->json([
            'redirect' => route('admin.customer.index'),
            'message' => __('User Updated successfully.')
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $user = User::where('role', 'user')->findorFail($id);

        $devices = Device::where('user_id', $user->id)->get();

        foreach ($devices as $device) {
            $deviceController = new DeviceController();
            $deviceController->destroy($device->id);
        }

        // delete groups
        Group::where('user_id', $user->id)->delete();

        // delete all contacts
        Contact::where('user_id', $user->id)->delete();

        $user->delete();

        return response()->json([
            'redirect' => route('admin.customer.index'),
            'message' => __('User deleted successfully.')
        ]);
    }

    public function customer_plan_edit($id)
    {
        $user = User::find($id);
        $plans = Plan::all();
        $resellers = User::where('role', 'admin')->get();
        return view('admin.customers.plan_edit', compact('user', 'plans', 'resellers'));
    }

    public function customer_plan_update(Request $request, $id)
    {
        // dd($request->all());
        try {
            // Enhanced validation
            $request->validate([
                'balance' => 'required',
                'business_initiated' => 'required',
                'plancre_deb' => 'nullable|in:credit,debit'
            ]);

            DB::beginTransaction();
            try {
                $user = User::findOrFail($id);
                $adminUser = User::findOrFail(Auth::id());

                // Handle credit logic for non-admin users (not ID 1)
                if ($adminUser->id != 1) {
                    $balanceUpdate = $request->balance_update ?? 0;
                    $planOperation = $request->plancre_deb;

                    // Only process if there's a balance operation
                    if ($planOperation && $balanceUpdate > 0) {
                        if ($planOperation === 'credit') {
                            // When crediting customer balance, check if admin has enough credits
                            if ($adminUser->credit < $balanceUpdate) {
                                DB::rollBack();
                                return response()->json([
                                    'message' => __('Insufficient credits. You need at least :credits credits to credit customer balance.', ['credits' => $balanceUpdate])
                                ], 400);
                            }

                            // Deduct credits from admin user
                            $adminUser->credit -= $balanceUpdate;
                        } else if ($planOperation === 'debit') {
                            // When debiting customer balance, increase admin credits
                            $adminUser->credit += $balanceUpdate;
                        }

                        $adminUser->save();
                    }
                }

                $balanceUpdate = $request->balance_update ?? 0;
                $planOperation = $request->plancre_deb;
                $newBalance = $user->balance;

                if ($planOperation && $balanceUpdate > 0) {

                    $newBalance = $planOperation === 'credit' ? $user->balance + $balanceUpdate : $user->balance - $balanceUpdate;

                    $order = new Order();
                    $order->payment_id = 1;
                    $order->plan_id = 1;
                    $order->admin_id = $adminUser->id;
                    $order->user_id = $user->id;
                    $order->gateway_id = 12;
                    $order->amount = $planOperation === 'credit' ? $balanceUpdate : -$balanceUpdate;
                    $order->tax = 0;
                    $order->status = 1;
                    $order->will_expire = null;
                    $order->meta = 'update user balance';

                    $order->save();
                }

                $user->plan = json_encode($request->plan_data);
                $user->plan_id = $request->user_plan_id;
                $user->reseller_id = $request->reseller_id ?? null;
                $user->balance = $newBalance;
                $user->business_initiated = $request->business_initiated;
                $user->save();

                DB::commit();

                return response()->json([
                    'redirect' => route('admin.customer.show', $id),
                    'message' => __('Customer plan updated successfully')
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Plan update failed', [
                    'error' => $e->getMessage(),
                    'user_id' => $id
                ]);

                return response()->json([
                    'message' => __('Failed to update customer plan. Please try again.')
                ], 500);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Unexpected error in plan update', [
                'error' => $e->getMessage(),
                'user_id' => $id
            ]);

            return response()->json([
                'message' => __('An unexpected error occurred. Please try again.')
            ], 500);
        }
    }
}
